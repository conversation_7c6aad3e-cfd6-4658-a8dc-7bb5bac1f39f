# -*- coding: utf-8 -*-

import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class AttendanceDataParser:
    """Parser for attendance data from various biometric devices"""
    
    @staticmethod
    def parse_raw_attendance(raw_data, device_type='zkteco'):
        """Parse raw attendance data based on device type"""
        if device_type == 'zkteco':
            return AttendanceDataParser._parse_zkteco_data(raw_data)
        elif device_type == 'hikvision':
            return AttendanceDataParser._parse_hikvision_data(raw_data)
        elif device_type == 'suprema':
            return AttendanceDataParser._parse_suprema_data(raw_data)
        else:
            return AttendanceDataParser._parse_generic_data(raw_data)
    
    @staticmethod
    def _parse_zkteco_data(raw_data):
        """Parse ZKTeco specific data format"""
        parsed_records = []
        
        for record in raw_data:
            try:
                parsed_record = {
                    'employee_id': record.get('user_id', ''),
                    'timestamp': record.get('timestamp'),
                    'verify_type': record.get('verify_type', 0),
                    'work_code': record.get('work_code', 0),
                }
                
                # Determine check-in/out based on verify type or work code
                if parsed_record['work_code'] == 0:  # Check-in
                    parsed_record['type'] = 'check_in'
                elif parsed_record['work_code'] == 1:  # Check-out
                    parsed_record['type'] = 'check_out'
                else:
                    # Auto-determine based on time patterns
                    parsed_record['type'] = AttendanceDataParser._auto_determine_type(
                        parsed_record['employee_id'], 
                        parsed_record['timestamp']
                    )
                
                parsed_records.append(parsed_record)
                
            except Exception as e:
                _logger.error(f"Error parsing ZKTeco record: {str(e)}")
                continue
        
        return parsed_records
    
    @staticmethod
    def _parse_hikvision_data(raw_data):
        """Parse Hikvision specific data format"""
        # Placeholder for Hikvision parsing logic
        return []
    
    @staticmethod
    def _parse_suprema_data(raw_data):
        """Parse Suprema specific data format"""
        # Placeholder for Suprema parsing logic
        return []
    
    @staticmethod
    def _parse_generic_data(raw_data):
        """Parse generic data format"""
        parsed_records = []
        
        for record in raw_data:
            try:
                parsed_record = {
                    'employee_id': str(record.get('employee_id', '')),
                    'timestamp': record.get('timestamp'),
                    'type': record.get('type', 'check_in'),
                }
                parsed_records.append(parsed_record)
            except Exception as e:
                _logger.error(f"Error parsing generic record: {str(e)}")
                continue
        
        return parsed_records
    
    @staticmethod
    def _auto_determine_type(employee_id, timestamp):
        """Auto-determine if record is check-in or check-out based on patterns"""
        # Simple logic: if it's before noon, likely check-in, otherwise check-out
        # Real implementation would be more sophisticated
        hour = timestamp.hour
        if hour < 12:
            return 'check_in'
        else:
            return 'check_out'
    
    @staticmethod
    def group_attendance_by_date(attendance_records):
        """Group attendance records by employee and date"""
        grouped = {}
        
        for record in attendance_records:
            employee_id = record['employee_id']
            date = record['timestamp'].date()
            key = f"{employee_id}_{date}"
            
            if key not in grouped:
                grouped[key] = {
                    'employee_id': employee_id,
                    'date': date,
                    'check_in': None,
                    'check_out': None,
                    'records': []
                }
            
            grouped[key]['records'].append(record)
            
            # Set check-in/out times
            if record['type'] == 'check_in':
                if not grouped[key]['check_in'] or record['timestamp'] < grouped[key]['check_in']:
                    grouped[key]['check_in'] = record['timestamp']
            elif record['type'] == 'check_out':
                if not grouped[key]['check_out'] or record['timestamp'] > grouped[key]['check_out']:
                    grouped[key]['check_out'] = record['timestamp']
        
        return list(grouped.values())
    
    @staticmethod
    def validate_attendance_data(attendance_record):
        """Validate attendance record data"""
        errors = []
        
        # Check required fields
        if not attendance_record.get('employee_id'):
            errors.append("Employee ID is required")
        
        if not attendance_record.get('date'):
            errors.append("Date is required")
        
        # Check time logic
        check_in = attendance_record.get('check_in')
        check_out = attendance_record.get('check_out')
        
        if check_in and check_out:
            if check_out <= check_in:
                errors.append("Check-out time must be after check-in time")
            
            # Check for reasonable work duration (not more than 24 hours)
            duration = check_out - check_in
            if duration.total_seconds() > 24 * 3600:
                errors.append("Work duration exceeds 24 hours")
        
        return errors
    
    @staticmethod
    def clean_attendance_data(attendance_records):
        """Clean and normalize attendance data"""
        cleaned_records = []
        
        for record in attendance_records:
            # Validate record
            errors = AttendanceDataParser.validate_attendance_data(record)
            if errors:
                _logger.warning(f"Invalid attendance record: {errors}")
                continue
            
            # Normalize data
            cleaned_record = {
                'employee_id': str(record['employee_id']).strip(),
                'attendance_date': record['date'],
                'check_in': record.get('check_in'),
                'check_out': record.get('check_out'),
            }
            
            cleaned_records.append(cleaned_record)
        
        return cleaned_records


class EmployeeDataParser:
    """Parser for employee data from biometric devices"""
    
    @staticmethod
    def parse_employee_data(raw_data, device_type='zkteco'):
        """Parse employee data based on device type"""
        if device_type == 'zkteco':
            return EmployeeDataParser._parse_zkteco_employees(raw_data)
        elif device_type == 'hikvision':
            return EmployeeDataParser._parse_hikvision_employees(raw_data)
        else:
            return EmployeeDataParser._parse_generic_employees(raw_data)
    
    @staticmethod
    def _parse_zkteco_employees(raw_data):
        """Parse ZKTeco employee data"""
        employees = []
        
        for emp_data in raw_data:
            try:
                employee = {
                    'employee_id': str(emp_data.get('user_id', '')),
                    'name': emp_data.get('name', '').strip(),
                    'badge_id': str(emp_data.get('card_no', '')),
                    'department': emp_data.get('department', ''),
                    'fingerprint_templates': emp_data.get('fp_count', 0),
                    'face_template': emp_data.get('face_count', 0) > 0,
                }
                
                # Clean empty values
                employee = {k: v for k, v in employee.items() if v}
                
                if employee.get('employee_id') and employee.get('name'):
                    employees.append(employee)
                    
            except Exception as e:
                _logger.error(f"Error parsing employee data: {str(e)}")
                continue
        
        return employees
    
    @staticmethod
    def _parse_hikvision_employees(raw_data):
        """Parse Hikvision employee data"""
        # Placeholder for Hikvision employee parsing
        return []
    
    @staticmethod
    def _parse_generic_employees(raw_data):
        """Parse generic employee data"""
        employees = []
        
        for emp_data in raw_data:
            try:
                employee = {
                    'employee_id': str(emp_data.get('id', '')),
                    'name': emp_data.get('name', '').strip(),
                    'badge_id': str(emp_data.get('badge', '')),
                    'department': emp_data.get('department', ''),
                }
                
                if employee.get('employee_id') and employee.get('name'):
                    employees.append(employee)
                    
            except Exception as e:
                _logger.error(f"Error parsing generic employee data: {str(e)}")
                continue
        
        return employees
    
    @staticmethod
    def validate_employee_data(employee_record):
        """Validate employee record data"""
        errors = []
        
        if not employee_record.get('employee_id'):
            errors.append("Employee ID is required")
        
        if not employee_record.get('name'):
            errors.append("Employee name is required")
        
        # Check for valid employee ID format
        emp_id = employee_record.get('employee_id', '')
        if not emp_id.isdigit() and not emp_id.isalnum():
            errors.append("Invalid employee ID format")
        
        return errors
