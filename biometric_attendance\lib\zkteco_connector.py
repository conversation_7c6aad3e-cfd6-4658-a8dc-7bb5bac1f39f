# -*- coding: utf-8 -*-

import struct
import socket
import serial
import logging
from datetime import datetime
from .device_connector import TCPDeviceConnector, SerialDeviceConnector

_logger = logging.getLogger(__name__)


class ZKTecoProtocol:
    """ZKTeco device protocol implementation"""
    
    # Command constants
    CMD_CONNECT = 1000
    CMD_EXIT = 1001
    CMD_ENABLEDEVICE = 1002
    CMD_DISABLEDEVICE = 1003
    CMD_GET_TIME = 201
    CMD_SET_TIME = 202
    CMD_USER_WRQ = 8
    CMD_USERTEMP_RRQ = 9
    CMD_ATTLOG_RRQ = 13
    CMD_CLEAR_DATA = 14
    CMD_CLEAR_ATTLOG = 15
    CMD_DELETE_USER = 18
    CMD_SET_USER = 8
    CMD_GET_FREE_SIZES = 50
    CMD_GET_USERS = 1104
    CMD_GET_ALL_USER = 1104
    CMD_GET_USER_TEMPLATE = 1105
    
    @staticmethod
    def create_header(command, session_id=0, reply_id=0):
        """Create ZKTeco protocol header"""
        return struct.pack('<HHHH', command, 0, session_id, reply_id)
    
    @staticmethod
    def create_packet(command, data=b'', session_id=0, reply_id=0):
        """Create complete ZKTeco packet"""
        header = ZKTecoProtocol.create_header(command, session_id, reply_id)
        size = len(data)
        checksum = 0
        for byte in data:
            checksum += byte
        checksum = checksum % 65536
        
        packet = struct.pack('<HHHH', command, checksum, session_id, reply_id) + data
        return packet
    
    @staticmethod
    def parse_response(data):
        """Parse ZKTeco response packet"""
        if len(data) < 8:
            return None
        
        command, checksum, session_id, reply_id = struct.unpack('<HHHH', data[:8])
        payload = data[8:]
        
        return {
            'command': command,
            'checksum': checksum,
            'session_id': session_id,
            'reply_id': reply_id,
            'payload': payload
        }


class ZKTecoTCPConnector(TCPDeviceConnector):
    """ZKTeco TCP/IP connector with protocol implementation"""
    
    def __init__(self, device_config):
        super().__init__(device_config)
        self.session_id = 0
        self.reply_id = 0
    
    def connect(self):
        """Connect to ZKTeco device via TCP"""
        super().connect()
        
        # Send connect command
        packet = ZKTecoProtocol.create_packet(ZKTecoProtocol.CMD_CONNECT)
        self.connection.send(packet)
        
        response = self.connection.recv(1024)
        parsed = ZKTecoProtocol.parse_response(response)
        
        if parsed and parsed['command'] == ZKTecoProtocol.CMD_CONNECT:
            self.session_id = parsed['session_id']
            _logger.info("ZKTeco device connected successfully")
            return True
        else:
            raise Exception("Failed to connect to ZKTeco device")
    
    def disconnect(self):
        """Disconnect from ZKTeco device"""
        if self.is_connected:
            try:
                packet = ZKTecoProtocol.create_packet(
                    ZKTecoProtocol.CMD_EXIT, 
                    session_id=self.session_id
                )
                self.connection.send(packet)
            except:
                pass
        
        super().disconnect()
    
    def get_employees(self):
        """Get employee list from ZKTeco device"""
        if not self.is_connected:
            raise Exception("Device not connected")
        
        try:
            # Request user data
            packet = ZKTecoProtocol.create_packet(
                ZKTecoProtocol.CMD_GET_USERS,
                session_id=self.session_id
            )
            self.connection.send(packet)
            
            response = self.connection.recv(4096)
            parsed = ZKTecoProtocol.parse_response(response)
            
            if not parsed:
                return []
            
            employees = self._parse_employee_data(parsed['payload'])
            return employees
            
        except Exception as e:
            _logger.error(f"Failed to get employees: {str(e)}")
            return []
    
    def get_attendance_records(self, start_date=None, end_date=None):
        """Get attendance records from ZKTeco device"""
        if not self.is_connected:
            raise Exception("Device not connected")
        
        try:
            # Request attendance log
            packet = ZKTecoProtocol.create_packet(
                ZKTecoProtocol.CMD_ATTLOG_RRQ,
                session_id=self.session_id
            )
            self.connection.send(packet)
            
            response = self.connection.recv(8192)
            parsed = ZKTecoProtocol.parse_response(response)
            
            if not parsed:
                return []
            
            records = self._parse_attendance_data(parsed['payload'], start_date, end_date)
            return records
            
        except Exception as e:
            _logger.error(f"Failed to get attendance records: {str(e)}")
            return []
    
    def _parse_employee_data(self, data):
        """Parse employee data from device response"""
        employees = []
        
        # ZKTeco user data format parsing
        # This is a simplified implementation
        # Real implementation would need proper protocol parsing
        
        try:
            offset = 0
            while offset < len(data) - 28:  # Minimum user record size
                # Parse user record (simplified)
                user_id = struct.unpack('<H', data[offset:offset+2])[0]
                offset += 2
                
                # Skip some bytes and get name
                offset += 2
                name_end = data.find(b'\x00', offset, offset + 24)
                if name_end == -1:
                    name_end = offset + 24
                
                name = data[offset:name_end].decode('utf-8', errors='ignore')
                offset = name_end + 1
                
                if user_id > 0 and name:
                    employees.append({
                        'employee_id': str(user_id),
                        'name': name,
                        'badge_id': str(user_id),
                    })
                
                # Skip to next record
                offset += 24
                
        except Exception as e:
            _logger.error(f"Error parsing employee data: {str(e)}")
        
        return employees
    
    def _parse_attendance_data(self, data, start_date=None, end_date=None):
        """Parse attendance data from device response"""
        records = []
        
        try:
            offset = 0
            while offset < len(data) - 16:  # Minimum attendance record size
                # Parse attendance record
                user_id = struct.unpack('<H', data[offset:offset+2])[0]
                offset += 2
                
                # Get timestamp
                timestamp = struct.unpack('<I', data[offset:offset+4])[0]
                offset += 4
                
                # Get verify type (check-in/out)
                verify_type = struct.unpack('<B', data[offset:offset+1])[0]
                offset += 1
                
                # Skip some bytes
                offset += 9
                
                if user_id > 0 and timestamp > 0:
                    # Convert timestamp to datetime
                    dt = datetime.fromtimestamp(timestamp)
                    
                    # Filter by date range if specified
                    if start_date and dt.date() < start_date:
                        continue
                    if end_date and dt.date() > end_date:
                        continue
                    
                    # Determine if it's check-in or check-out
                    # This is simplified - real logic would be more complex
                    is_checkin = verify_type in [0, 1]  # Fingerprint or password
                    
                    record = {
                        'employee_id': str(user_id),
                        'attendance_date': dt.date(),
                        'check_in' if is_checkin else 'check_out': dt,
                    }
                    
                    records.append(record)
                
        except Exception as e:
            _logger.error(f"Error parsing attendance data: {str(e)}")
        
        return records


class ZKTecoSerialConnector(SerialDeviceConnector):
    """ZKTeco Serial connector"""
    
    def __init__(self, device_config):
        super().__init__(device_config)
        self.session_id = 0
    
    def get_employees(self):
        """Get employee list via serial connection"""
        # Simplified implementation for serial connection
        # Real implementation would use proper ZKTeco serial protocol
        return []
    
    def get_attendance_records(self, start_date=None, end_date=None):
        """Get attendance records via serial connection"""
        # Simplified implementation for serial connection
        # Real implementation would use proper ZKTeco serial protocol
        return []
