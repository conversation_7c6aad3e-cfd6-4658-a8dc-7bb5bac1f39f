<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Demo Biometric Devices -->
    <record id="demo_device_main" model="biometric.device">
        <field name="name">Main Office ZKTeco</field>
        <field name="device_type">zkteco</field>
        <field name="connection_type">tcp</field>
        <field name="ip_address">*************</field>
        <field name="port">4370</field>
        <field name="device_id">1</field>
        <field name="timeout">30</field>
        <field name="status">disconnected</field>
    </record>

    <record id="demo_device_branch" model="biometric.device">
        <field name="name">Branch Office Hikvision</field>
        <field name="device_type">hikvision</field>
        <field name="connection_type">tcp</field>
        <field name="ip_address">*************</field>
        <field name="port">8000</field>
        <field name="device_id">2</field>
        <field name="timeout">30</field>
        <field name="status">disconnected</field>
    </record>

    <record id="demo_device_warehouse" model="biometric.device">
        <field name="name">Warehouse Serial Device</field>
        <field name="device_type">zkteco</field>
        <field name="connection_type">serial</field>
        <field name="serial_port">COM1</field>
        <field name="baud_rate">9600</field>
        <field name="device_id">3</field>
        <field name="timeout">30</field>
        <field name="status">disconnected</field>
    </record>

    <!-- Demo Employees -->
    <record id="demo_employee_001" model="biometric.employee">
        <field name="name">Ahmed Mohamed</field>
        <field name="employee_id">001</field>
        <field name="device_id" ref="demo_device_main"/>
        <field name="badge_id">B001</field>
        <field name="department">IT Department</field>
        <field name="position">Software Developer</field>
        <field name="phone">+20123456789</field>
        <field name="email"><EMAIL></field>
        <field name="work_rule_id" ref="biometric_attendance.work_rule_standard"/>
        <field name="fingerprint_templates">2</field>
        <field name="face_template">True</field>
        <field name="hire_date">2023-01-15</field>
    </record>

    <record id="demo_employee_002" model="biometric.employee">
        <field name="name">Fatima Ali</field>
        <field name="employee_id">002</field>
        <field name="device_id" ref="demo_device_main"/>
        <field name="badge_id">B002</field>
        <field name="department">HR Department</field>
        <field name="position">HR Specialist</field>
        <field name="phone">+20123456790</field>
        <field name="email"><EMAIL></field>
        <field name="work_rule_id" ref="biometric_attendance.work_rule_flexible"/>
        <field name="fingerprint_templates">1</field>
        <field name="face_template">False</field>
        <field name="hire_date">2023-02-01</field>
    </record>

    <record id="demo_employee_003" model="biometric.employee">
        <field name="name">Omar Hassan</field>
        <field name="employee_id">003</field>
        <field name="device_id" ref="demo_device_branch"/>
        <field name="badge_id">B003</field>
        <field name="department">Sales Department</field>
        <field name="position">Sales Manager</field>
        <field name="phone">+20123456791</field>
        <field name="email"><EMAIL></field>
        <field name="work_rule_id" ref="biometric_attendance.work_rule_standard"/>
        <field name="fingerprint_templates">2</field>
        <field name="face_template">True</field>
        <field name="hire_date">2022-12-01</field>
    </record>

    <record id="demo_employee_004" model="biometric.employee">
        <field name="name">Maryam Khaled</field>
        <field name="employee_id">004</field>
        <field name="device_id" ref="demo_device_branch"/>
        <field name="badge_id">B004</field>
        <field name="department">Finance Department</field>
        <field name="position">Accountant</field>
        <field name="phone">+***********</field>
        <field name="email"><EMAIL></field>
        <field name="work_rule_id" ref="biometric_attendance.work_rule_standard"/>
        <field name="fingerprint_templates">1</field>
        <field name="face_template">False</field>
        <field name="hire_date">2023-03-15</field>
    </record>

    <record id="demo_employee_005" model="biometric.employee">
        <field name="name">Youssef Ibrahim</field>
        <field name="employee_id">005</field>
        <field name="device_id" ref="demo_device_warehouse"/>
        <field name="badge_id">B005</field>
        <field name="department">Warehouse</field>
        <field name="position">Warehouse Supervisor</field>
        <field name="phone">+***********</field>
        <field name="email"><EMAIL></field>
        <field name="work_rule_id" ref="biometric_attendance.work_rule_shift"/>
        <field name="fingerprint_templates">2</field>
        <field name="face_template">True</field>
        <field name="hire_date">2022-11-01</field>
    </record>

    <!-- Demo Attendance Records for Current Month -->
    <record id="demo_attendance_001_today" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_001"/>
        <field name="device_id" ref="demo_device_main"/>
        <field name="attendance_date" eval="(datetime.datetime.now()).strftime('%Y-%m-%d')"/>
        <field name="check_in" eval="(datetime.datetime.now().replace(hour=8, minute=15, second=0, microsecond=0))"/>
        <field name="check_out" eval="(datetime.datetime.now().replace(hour=17, minute=30, second=0, microsecond=0))"/>
    </record>

    <record id="demo_attendance_002_today" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_002"/>
        <field name="device_id" ref="demo_device_main"/>
        <field name="attendance_date" eval="(datetime.datetime.now()).strftime('%Y-%m-%d')"/>
        <field name="check_in" eval="(datetime.datetime.now().replace(hour=9, minute=45, second=0, microsecond=0))"/>
        <field name="check_out" eval="(datetime.datetime.now().replace(hour=17, minute=15, second=0, microsecond=0))"/>
    </record>

    <record id="demo_attendance_003_today" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_003"/>
        <field name="device_id" ref="demo_device_branch"/>
        <field name="attendance_date" eval="(datetime.datetime.now()).strftime('%Y-%m-%d')"/>
        <field name="check_in" eval="(datetime.datetime.now().replace(hour=7, minute=55, second=0, microsecond=0))"/>
        <field name="check_out" eval="(datetime.datetime.now().replace(hour=18, minute=0, second=0, microsecond=0))"/>
    </record>

    <!-- Demo Attendance Records for Yesterday -->
    <record id="demo_attendance_001_yesterday" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_001"/>
        <field name="device_id" ref="demo_device_main"/>
        <field name="attendance_date" eval="(datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="check_in" eval="(datetime.datetime.now() - datetime.timedelta(days=1)).replace(hour=8, minute=30, second=0, microsecond=0)"/>
        <field name="check_out" eval="(datetime.datetime.now() - datetime.timedelta(days=1)).replace(hour=17, minute=45, second=0, microsecond=0)"/>
    </record>

    <record id="demo_attendance_002_yesterday" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_002"/>
        <field name="device_id" ref="demo_device_main"/>
        <field name="attendance_date" eval="(datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="check_in" eval="(datetime.datetime.now() - datetime.timedelta(days=1)).replace(hour=9, minute=0, second=0, microsecond=0)"/>
        <field name="check_out" eval="(datetime.datetime.now() - datetime.timedelta(days=1)).replace(hour=17, minute=0, second=0, microsecond=0)"/>
    </record>

    <!-- Demo Absence Record -->
    <record id="demo_attendance_004_absent" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_004"/>
        <field name="device_id" ref="demo_device_branch"/>
        <field name="attendance_date" eval="(datetime.datetime.now()).strftime('%Y-%m-%d')"/>
        <field name="is_absent">True</field>
        <field name="notes">Employee called in sick</field>
    </record>

    <!-- Demo Holiday Record -->
    <record id="demo_attendance_005_holiday" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_005"/>
        <field name="device_id" ref="demo_device_warehouse"/>
        <field name="attendance_date" eval="(datetime.datetime.now()).strftime('%Y-%m-%d')"/>
        <field name="is_holiday">True</field>
        <field name="notes">National Holiday</field>
    </record>
</odoo>
