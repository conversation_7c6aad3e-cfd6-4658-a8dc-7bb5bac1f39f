# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class BiometricEmployee(models.Model):
    _name = 'biometric.employee'
    _description = 'Biometric Employee'
    _order = 'employee_id'

    name = fields.Char('Employee Name', required=True)
    employee_id = fields.Char('Employee ID', required=True, help='Employee ID from biometric device')
    device_id = fields.Many2one('biometric.device', string='Device', required=True, ondelete='cascade')
    
    # Personal Information
    badge_id = fields.Char('Badge ID')
    department = fields.Char('Department')
    position = fields.Char('Position')
    phone = fields.Char('Phone')
    email = fields.Char('Email')
    
    # Biometric Data
    fingerprint_templates = fields.Integer('Fingerprint Templates', default=0)
    face_template = fields.Bo<PERSON>an('Face Template', default=False)
    card_number = fields.Char('Card Number')
    
    # Work Settings
    work_rule_id = fields.Many2one('biometric.work.rule', string='Work Rule')
    active = fields.Bo<PERSON>an('Active', default=True)
    hire_date = fields.Date('Hire Date')
    
    # Status
    last_attendance = fields.Datetime('Last Attendance', readonly=True)
    status = fields.Selection([
        ('present', 'Present'),
        ('absent', 'Absent'),
        ('late', 'Late'),
        ('early_departure', 'Early Departure')
    ], string='Current Status', compute='_compute_current_status', store=True)
    
    # Relations
    attendance_ids = fields.One2many('biometric.attendance', 'employee_id', string='Attendance Records')
    
    # Statistics
    total_attendance_days = fields.Integer('Total Attendance Days', compute='_compute_statistics')
    total_late_days = fields.Integer('Total Late Days', compute='_compute_statistics')
    total_absent_days = fields.Integer('Total Absent Days', compute='_compute_statistics')
    average_work_hours = fields.Float('Average Work Hours', compute='_compute_statistics')
    
    _sql_constraints = [
        ('unique_employee_device', 'unique(employee_id, device_id)', 
         'Employee ID must be unique per device!'),
    ]
    
    @api.depends('attendance_ids', 'attendance_ids.check_in', 'attendance_ids.check_out')
    def _compute_current_status(self):
        for employee in self:
            today_attendance = employee.attendance_ids.filtered(
                lambda r: r.attendance_date == fields.Date.today()
            )
            if today_attendance:
                latest_record = today_attendance.sorted('check_in', reverse=True)[0]
                if latest_record.is_late:
                    employee.status = 'late'
                elif latest_record.early_departure:
                    employee.status = 'early_departure'
                else:
                    employee.status = 'present'
            else:
                employee.status = 'absent'
    
    @api.depends('attendance_ids')
    def _compute_statistics(self):
        for employee in self:
            attendances = employee.attendance_ids
            employee.total_attendance_days = len(attendances.filtered('check_in'))
            employee.total_late_days = len(attendances.filtered('is_late'))
            employee.total_absent_days = len(attendances.filtered(lambda r: not r.check_in))
            
            if attendances:
                total_hours = sum(attendances.mapped('worked_hours'))
                employee.average_work_hours = total_hours / len(attendances) if attendances else 0
            else:
                employee.average_work_hours = 0
    
    def action_view_attendance(self):
        """Open attendance records for this employee"""
        self.ensure_one()
        return {
            'name': _('Attendance Records - %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'biometric.attendance',
            'view_mode': 'tree,form',
            'domain': [('employee_id', '=', self.id)],
            'context': {'default_employee_id': self.id, 'default_device_id': self.device_id.id},
        }
    
    def action_manual_checkin(self):
        """Manual check-in for employee"""
        self.ensure_one()
        attendance_obj = self.env['biometric.attendance']
        
        # Check if already checked in today
        today_attendance = attendance_obj.search([
            ('employee_id', '=', self.id),
            ('attendance_date', '=', fields.Date.today()),
            ('check_in', '!=', False)
        ])
        
        if today_attendance and not today_attendance.check_out:
            raise ValidationError(_('Employee %s is already checked in today.') % self.name)
        
        # Create new attendance record
        attendance_obj.create({
            'employee_id': self.id,
            'device_id': self.device_id.id,
            'attendance_date': fields.Date.today(),
            'check_in': fields.Datetime.now(),
            'manual_entry': True,
        })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Check-in Successful'),
                'message': _('Manual check-in recorded for %s') % self.name,
                'type': 'success',
            }
        }
    
    def action_manual_checkout(self):
        """Manual check-out for employee"""
        self.ensure_one()
        attendance_obj = self.env['biometric.attendance']
        
        # Find today's attendance without check-out
        today_attendance = attendance_obj.search([
            ('employee_id', '=', self.id),
            ('attendance_date', '=', fields.Date.today()),
            ('check_in', '!=', False),
            ('check_out', '=', False)
        ], limit=1)
        
        if not today_attendance:
            raise ValidationError(_('No check-in record found for %s today.') % self.name)
        
        # Update with check-out time
        today_attendance.write({
            'check_out': fields.Datetime.now(),
            'manual_entry': True,
        })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Check-out Successful'),
                'message': _('Manual check-out recorded for %s') % self.name,
                'type': 'success',
            }
        }
