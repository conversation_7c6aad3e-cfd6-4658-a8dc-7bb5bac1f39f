# -*- coding: utf-8 -*-

import socket
import serial
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)


class BaseDeviceConnector:
    """Base class for biometric device connectors"""
    
    def __init__(self, device_config):
        self.device_config = device_config
        self.connection = None
        self.is_connected = False
    
    def connect(self):
        """Connect to the device"""
        raise NotImplementedError("Subclasses must implement connect method")
    
    def disconnect(self):
        """Disconnect from the device"""
        raise NotImplementedError("Subclasses must implement disconnect method")
    
    def get_employees(self):
        """Get employee list from device"""
        raise NotImplementedError("Subclasses must implement get_employees method")
    
    def get_attendance_records(self, start_date=None, end_date=None):
        """Get attendance records from device"""
        raise NotImplementedError("Subclasses must implement get_attendance_records method")
    
    def test_connection(self):
        """Test device connection"""
        try:
            self.connect()
            self.disconnect()
            return True
        except Exception as e:
            _logger.error(f"Connection test failed: {str(e)}")
            return False


class TCPDeviceConnector(BaseDeviceConnector):
    """TCP/IP device connector"""
    
    def connect(self):
        """Connect via TCP/IP"""
        try:
            self.connection = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.connection.settimeout(self.device_config.get('timeout', 30))
            self.connection.connect((
                self.device_config['ip_address'], 
                self.device_config['port']
            ))
            self.is_connected = True
            _logger.info(f"Connected to device at {self.device_config['ip_address']}:{self.device_config['port']}")
            return True
        except Exception as e:
            _logger.error(f"TCP connection failed: {str(e)}")
            self.is_connected = False
            raise
    
    def disconnect(self):
        """Disconnect TCP connection"""
        if self.connection:
            try:
                self.connection.close()
                self.is_connected = False
                _logger.info("TCP connection closed")
            except Exception as e:
                _logger.error(f"Error closing TCP connection: {str(e)}")
    
    def send_command(self, command):
        """Send command to device"""
        if not self.is_connected:
            raise Exception("Device not connected")
        
        try:
            self.connection.send(command.encode())
            response = self.connection.recv(1024)
            return response.decode()
        except Exception as e:
            _logger.error(f"Command send failed: {str(e)}")
            raise


class SerialDeviceConnector(BaseDeviceConnector):
    """Serial port device connector"""
    
    def connect(self):
        """Connect via Serial port"""
        try:
            self.connection = serial.Serial(
                port=self.device_config['serial_port'],
                baudrate=self.device_config.get('baud_rate', 9600),
                timeout=self.device_config.get('timeout', 30)
            )
            self.is_connected = self.connection.is_open
            _logger.info(f"Connected to device at {self.device_config['serial_port']}")
            return True
        except Exception as e:
            _logger.error(f"Serial connection failed: {str(e)}")
            self.is_connected = False
            raise
    
    def disconnect(self):
        """Disconnect serial connection"""
        if self.connection and self.connection.is_open:
            try:
                self.connection.close()
                self.is_connected = False
                _logger.info("Serial connection closed")
            except Exception as e:
                _logger.error(f"Error closing serial connection: {str(e)}")
    
    def send_command(self, command):
        """Send command to device"""
        if not self.is_connected:
            raise Exception("Device not connected")
        
        try:
            self.connection.write(command.encode())
            response = self.connection.readline()
            return response.decode().strip()
        except Exception as e:
            _logger.error(f"Command send failed: {str(e)}")
            raise


class DeviceConnectorFactory:
    """Factory class to create appropriate device connector"""
    
    @staticmethod
    def create_connector(device_record):
        """Create connector based on device configuration"""
        device_config = {
            'ip_address': device_record.ip_address,
            'port': device_record.port,
            'serial_port': device_record.serial_port,
            'baud_rate': device_record.baud_rate,
            'timeout': device_record.timeout,
            'device_type': device_record.device_type,
            'device_id': device_record.device_id,
            'password': device_record.password,
        }
        
        if device_record.connection_type == 'tcp':
            if device_record.device_type == 'zkteco':
                from .zkteco_connector import ZKTecoTCPConnector
                return ZKTecoTCPConnector(device_config)
            else:
                return TCPDeviceConnector(device_config)
        
        elif device_record.connection_type == 'serial':
            if device_record.device_type == 'zkteco':
                from .zkteco_connector import ZKTecoSerialConnector
                return ZKTecoSerialConnector(device_config)
            else:
                return SerialDeviceConnector(device_config)
        
        else:
            raise ValueError(f"Unsupported connection type: {device_record.connection_type}")


class DeviceDataSynchronizer:
    """Class to handle data synchronization with devices"""
    
    def __init__(self, device_record, odoo_env):
        self.device_record = device_record
        self.env = odoo_env
        self.connector = DeviceConnectorFactory.create_connector(device_record)
    
    def sync_employees(self):
        """Synchronize employees from device"""
        try:
            self.connector.connect()
            device_employees = self.connector.get_employees()
            
            for emp_data in device_employees:
                self._create_or_update_employee(emp_data)
            
            self.connector.disconnect()
            self.device_record.last_sync = datetime.now()
            return len(device_employees)
            
        except Exception as e:
            _logger.error(f"Employee sync failed for device {self.device_record.name}: {str(e)}")
            raise
    
    def sync_attendance(self, start_date=None, end_date=None):
        """Synchronize attendance records from device"""
        try:
            self.connector.connect()
            attendance_records = self.connector.get_attendance_records(start_date, end_date)
            
            for att_data in attendance_records:
                self._create_or_update_attendance(att_data)
            
            self.connector.disconnect()
            self.device_record.last_sync = datetime.now()
            return len(attendance_records)
            
        except Exception as e:
            _logger.error(f"Attendance sync failed for device {self.device_record.name}: {str(e)}")
            raise
    
    def _create_or_update_employee(self, emp_data):
        """Create or update employee record"""
        employee_obj = self.env['biometric.employee']
        existing = employee_obj.search([
            ('employee_id', '=', emp_data['employee_id']),
            ('device_id', '=', self.device_record.id)
        ])
        
        if existing:
            existing.write(emp_data)
        else:
            emp_data['device_id'] = self.device_record.id
            employee_obj.create(emp_data)
    
    def _create_or_update_attendance(self, att_data):
        """Create or update attendance record"""
        attendance_obj = self.env['biometric.attendance']
        employee_obj = self.env['biometric.employee']
        
        # Find employee
        employee = employee_obj.search([
            ('employee_id', '=', att_data['employee_id']),
            ('device_id', '=', self.device_record.id)
        ], limit=1)
        
        if not employee:
            _logger.warning(f"Employee {att_data['employee_id']} not found for attendance record")
            return
        
        # Check if record exists
        existing = attendance_obj.search([
            ('employee_id', '=', employee.id),
            ('attendance_date', '=', att_data['attendance_date'])
        ])
        
        att_data['employee_id'] = employee.id
        att_data['device_id'] = self.device_record.id
        
        if existing:
            existing.write(att_data)
        else:
            attendance_obj.create(att_data)
