# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta


class BiometricSyncWizard(models.TransientModel):
    _name = 'biometric.sync.wizard'
    _description = 'Biometric Device Sync Wizard'

    device_ids = fields.Many2many('biometric.device', string='Devices', required=True)
    sync_type = fields.Selection([
        ('employees', 'Employees Only'),
        ('attendance', 'Attendance Only'),
        ('all', 'All Data')
    ], string='Sync Type', required=True, default='all')
    
    date_from = fields.Date('Date From (for attendance)', 
                           default=lambda self: fields.Date.today() - timedelta(days=7))
    date_to = fields.Date('Date To (for attendance)', default=fields.Date.today)
    
    force_sync = fields.Bo<PERSON>an('Force Sync', default=False,
                               help='Force synchronization even if device was recently synced')
    test_connection = fields.Boolean('Test Connection First', default=True)
    
    # Results
    sync_results = fields.Text('Sync Results', readonly=True)
    
    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for wizard in self:
            if wizard.date_from > wizard.date_to:
                raise ValidationError(_('Date From must be before Date To'))
    
    def action_test_connections(self):
        """Test connections to selected devices"""
        self.ensure_one()
        results = []
        
        for device in self.device_ids:
            try:
                device.test_connection()
                results.append(f"✓ {device.name}: Connection successful")
            except Exception as e:
                results.append(f"✗ {device.name}: Connection failed - {str(e)}")
        
        self.sync_results = '\n'.join(results)
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'biometric.sync.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }
    
    def action_sync_devices(self):
        """Perform synchronization with selected devices"""
        self.ensure_one()
        
        if self.test_connection:
            # Test connections first
            failed_devices = []
            for device in self.device_ids:
                try:
                    device.test_connection()
                except Exception as e:
                    failed_devices.append(f"{device.name}: {str(e)}")
            
            if failed_devices:
                raise UserError(_('Connection test failed for devices:\n%s') % '\n'.join(failed_devices))
        
        # Perform synchronization
        results = []
        total_employees = 0
        total_attendance = 0
        
        for device in self.device_ids:
            try:
                device_results = []
                
                if self.sync_type in ['employees', 'all']:
                    emp_count = self._sync_device_employees(device)
                    total_employees += emp_count
                    device_results.append(f"Employees: {emp_count}")
                
                if self.sync_type in ['attendance', 'all']:
                    att_count = self._sync_device_attendance(device)
                    total_attendance += att_count
                    device_results.append(f"Attendance: {att_count}")
                
                results.append(f"✓ {device.name}: {', '.join(device_results)}")
                
            except Exception as e:
                results.append(f"✗ {device.name}: Sync failed - {str(e)}")
        
        # Update results
        summary = f"Sync completed!\nTotal Employees: {total_employees}\nTotal Attendance Records: {total_attendance}\n\nDevice Results:\n"
        self.sync_results = summary + '\n'.join(results)
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'biometric.sync.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }
    
    def _sync_device_employees(self, device):
        """Sync employees from a specific device"""
        try:
            from ..lib.device_connector import DeviceDataSynchronizer
            synchronizer = DeviceDataSynchronizer(device, self.env)
            count = synchronizer.sync_employees()
            device.last_sync = fields.Datetime.now()
            return count
        except Exception as e:
            raise UserError(_('Employee sync failed for device %s: %s') % (device.name, str(e)))
    
    def _sync_device_attendance(self, device):
        """Sync attendance from a specific device"""
        try:
            from ..lib.device_connector import DeviceDataSynchronizer
            synchronizer = DeviceDataSynchronizer(device, self.env)
            count = synchronizer.sync_attendance(self.date_from, self.date_to)
            device.last_sync = fields.Datetime.now()
            return count
        except Exception as e:
            raise UserError(_('Attendance sync failed for device %s: %s') % (device.name, str(e)))
    
    def action_schedule_sync(self):
        """Schedule automatic synchronization"""
        self.ensure_one()
        
        # Create or update scheduled action for these devices
        cron_name = f"Auto Sync - {', '.join(self.device_ids.mapped('name'))}"
        
        existing_cron = self.env['ir.cron'].search([
            ('name', '=', cron_name)
        ], limit=1)
        
        cron_code = f"""
# Auto sync specific devices
device_ids = {self.device_ids.ids}
devices = env['biometric.device'].browse(device_ids)
for device in devices:
    try:
        if '{self.sync_type}' in ['employees', 'all']:
            device.sync_employees()
        if '{self.sync_type}' in ['attendance', 'all']:
            device.sync_attendance()
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.error(f"Scheduled sync failed for device {{device.name}}: {{str(e)}}")
        """
        
        if existing_cron:
            existing_cron.write({
                'code': cron_code,
                'active': True,
            })
        else:
            self.env['ir.cron'].create({
                'name': cron_name,
                'model_id': self.env.ref('biometric_attendance.model_biometric_device').id,
                'state': 'code',
                'code': cron_code,
                'interval_number': 30,
                'interval_type': 'minutes',
                'numbercall': -1,
                'active': True,
                'doall': False,
            })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Scheduled'),
                'message': _('Automatic synchronization has been scheduled for selected devices'),
                'type': 'success',
            }
        }
    
    def action_clear_device_data(self):
        """Clear all data from selected devices (employees and attendance)"""
        self.ensure_one()
        
        if not self.force_sync:
            raise UserError(_('Force Sync must be enabled to clear device data'))
        
        for device in self.device_ids:
            # Delete attendance records
            device.attendance_ids.unlink()
            # Delete employees
            device.employee_ids.unlink()
            # Reset statistics
            device.write({
                'total_employees': 0,
                'total_records': 0,
                'last_sync': False,
            })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Data Cleared'),
                'message': _('All data has been cleared from selected devices'),
                'type': 'warning',
            }
        }
