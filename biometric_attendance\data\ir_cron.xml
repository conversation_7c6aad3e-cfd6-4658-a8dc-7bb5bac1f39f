<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Scheduled Action: Auto Sync Devices -->
    <record id="ir_cron_auto_sync_devices" model="ir.cron">
        <field name="name">Biometric: Auto Sync Devices</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="state">code</field>
        <field name="code">
# Auto sync all active devices
devices = env['biometric.device'].search([('active', '=', True)])
for device in devices:
    try:
        device.sync_all_data()
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.error(f"Auto sync failed for device {device.name}: {str(e)}")
        </field>
        <field name="interval_number">30</field>
        <field name="interval_type">minutes</field>
        <field name="numbercall">-1</field>
        <field name="active">False</field>
        <field name="doall">False</field>
    </record>

    <!-- Scheduled Action: Create Daily Attendance Records -->
    <record id="ir_cron_create_daily_records" model="ir.cron">
        <field name="name">Biometric: Create Daily Attendance Records</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="state">code</field>
        <field name="code">
# Create daily attendance records for all active employees
model.create_daily_attendance_records()
        </field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="nextcall" eval="(DateTime.now() + timedelta(days=1)).replace(hour=0, minute=30, second=0)"/>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="doall">False</field>
    </record>

    <!-- Scheduled Action: Mark Absent Employees -->
    <record id="ir_cron_mark_absent" model="ir.cron">
        <field name="name">Biometric: Mark Absent Employees</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="state">code</field>
        <field name="code">
# Mark employees as absent after threshold time
model.mark_absent_employees()
        </field>
        <field name="interval_number">1</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="doall">False</field>
    </record>

    <!-- Scheduled Action: Cleanup Old Data -->
    <record id="ir_cron_cleanup_old_data" model="ir.cron">
        <field name="name">Biometric: Cleanup Old Attendance Data</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="state">code</field>
        <field name="code">
# Cleanup old attendance data based on retention policy
config = env['ir.config_parameter'].sudo()
retention_days = int(config.get_param('biometric_attendance.data_retention_days', 365))

if retention_days > 0:
    from datetime import datetime, timedelta
    cutoff_date = (datetime.now() - timedelta(days=retention_days)).date()
    old_records = env['biometric.attendance'].search([
        ('attendance_date', '&lt;', cutoff_date)
    ])
    count = len(old_records)
    old_records.unlink()
    
    import logging
    _logger = logging.getLogger(__name__)
    _logger.info(f"Cleaned up {count} old attendance records older than {retention_days} days")
        </field>
        <field name="interval_number">1</field>
        <field name="interval_type">weeks</field>
        <field name="nextcall" eval="(DateTime.now() + timedelta(weeks=1)).replace(hour=2, minute=0, second=0)"/>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="doall">False</field>
    </record>

    <!-- Scheduled Action: Update Device Statistics -->
    <record id="ir_cron_update_device_stats" model="ir.cron">
        <field name="name">Biometric: Update Device Statistics</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="state">code</field>
        <field name="code">
# Update device statistics
devices = env['biometric.device'].search([])
for device in devices:
    device.total_employees = len(device.employee_ids)
    device.total_records = len(device.attendance_ids)
        </field>
        <field name="interval_number">6</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="doall">False</field>
    </record>

    <!-- Scheduled Action: Send Late Notifications -->
    <record id="ir_cron_late_notifications" model="ir.cron">
        <field name="name">Biometric: Send Late Arrival Notifications</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="state">code</field>
        <field name="code">
# Send notifications for late arrivals
config = env['ir.config_parameter'].sudo()
late_notification = config.get_param('biometric_attendance.late_notification', False)

if late_notification:
    today = fields.Date.today()
    late_records = env['biometric.attendance'].search([
        ('attendance_date', '=', today),
        ('is_late', '=', True),
        ('check_in', '!=', False)
    ])
    
    # Here you would implement notification logic
    # For example, sending emails or creating internal messages
    for record in late_records:
        # Create notification message
        message = f"Employee {record.employee_id.name} arrived late by {record.late_minutes} minutes"
        # You can implement email sending or other notification methods here
        pass
        </field>
        <field name="interval_number">1</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="active">False</field>
        <field name="doall">False</field>
    </record>

    <!-- Scheduled Action: Send Absence Notifications -->
    <record id="ir_cron_absence_notifications" model="ir.cron">
        <field name="name">Biometric: Send Absence Notifications</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="state">code</field>
        <field name="code">
# Send notifications for absences
config = env['ir.config_parameter'].sudo()
absence_notification = config.get_param('biometric_attendance.absence_notification', False)

if absence_notification:
    today = fields.Date.today()
    absent_records = env['biometric.attendance'].search([
        ('attendance_date', '=', today),
        ('is_absent', '=', True)
    ])
    
    # Here you would implement notification logic
    for record in absent_records:
        # Create notification message
        message = f"Employee {record.employee_id.name} is absent today"
        # You can implement email sending or other notification methods here
        pass
        </field>
        <field name="interval_number">2</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="active">False</field>
        <field name="doall">False</field>
    </record>
</odoo>
