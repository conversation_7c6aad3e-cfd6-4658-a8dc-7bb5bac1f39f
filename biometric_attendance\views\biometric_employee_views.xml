<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Biometric Employee Tree View -->
    <record id="view_biometric_employee_tree" model="ir.ui.view">
        <field name="name">biometric.employee.tree</field>
        <field name="model">biometric.employee</field>
        <field name="arch" type="xml">
            <tree string="Biometric Employees" decoration-success="status=='present'" decoration-warning="status=='late'" decoration-danger="status=='absent'">
                <field name="employee_id"/>
                <field name="name"/>
                <field name="device_id"/>
                <field name="department"/>
                <field name="position"/>
                <field name="status"/>
                <field name="last_attendance"/>
                <field name="total_attendance_days"/>
                <field name="total_late_days"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Biometric Employee Form View -->
    <record id="view_biometric_employee_form" model="ir.ui.view">
        <field name="name">biometric.employee.form</field>
        <field name="model">biometric.employee</field>
        <field name="arch" type="xml">
            <form string="Biometric Employee">
                <header>
                    <button name="action_manual_checkin" string="Manual Check-in" type="object" class="btn-primary"/>
                    <button name="action_manual_checkout" string="Manual Check-out" type="object" class="btn-secondary"/>
                    <field name="status" widget="statusbar" statusbar_visible="present,late,absent"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_attendance" type="object" class="oe_stat_button" icon="fa-calendar">
                            <field name="total_attendance_days" widget="statinfo" string="Attendance Days"/>
                        </button>
                        <button name="%(action_biometric_attendance)d" type="action" class="oe_stat_button" icon="fa-clock-o" context="{'search_default_employee_id': active_id}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">
                                    <field name="total_late_days"/>
                                </span>
                                <span class="o_stat_text">Late Days</span>
                            </div>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Inactive" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Employee Name"/>
                        </h1>
                        <h2>
                            <field name="employee_id" placeholder="Employee ID"/>
                        </h2>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="device_id"/>
                            <field name="badge_id"/>
                            <field name="department"/>
                            <field name="position"/>
                            <field name="work_rule_id"/>
                            <field name="active"/>
                        </group>
                        <group name="contact_info">
                            <field name="phone"/>
                            <field name="email"/>
                            <field name="hire_date"/>
                            <field name="last_attendance"/>
                        </group>
                    </group>
                    
                    <group name="biometric_info" string="Biometric Information">
                        <group>
                            <field name="fingerprint_templates"/>
                            <field name="face_template"/>
                        </group>
                        <group>
                            <field name="card_number"/>
                        </group>
                    </group>
                    
                    <group name="statistics" string="Statistics">
                        <group>
                            <field name="total_attendance_days"/>
                            <field name="total_late_days"/>
                        </group>
                        <group>
                            <field name="total_absent_days"/>
                            <field name="average_work_hours"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Attendance Records" name="attendance">
                            <field name="attendance_ids">
                                <tree decoration-success="status=='present'" decoration-warning="status=='late'" decoration-danger="status=='absent'">
                                    <field name="attendance_date"/>
                                    <field name="check_in"/>
                                    <field name="check_out"/>
                                    <field name="worked_hours"/>
                                    <field name="late_minutes"/>
                                    <field name="status"/>
                                    <field name="manual_entry"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Biometric Employee Kanban View -->
    <record id="view_biometric_employee_kanban" model="ir.ui.view">
        <field name="name">biometric.employee.kanban</field>
        <field name="model">biometric.employee</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="employee_id"/>
                <field name="status"/>
                <field name="department"/>
                <field name="last_attendance"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                    <small class="o_kanban_record_subtitle text-muted">
                                        ID: <field name="employee_id"/>
                                    </small>
                                </div>
                                <div class="o_kanban_record_body">
                                    <field name="department"/>
                                    <br/>
                                    <t t-if="record.last_attendance.raw_value">
                                        Last: <field name="last_attendance"/>
                                    </t>
                                </div>
                            </div>
                            <div class="o_kanban_record_bottom">
                                <div class="oe_kanban_bottom_left">
                                    <span class="badge" t-attf-class="badge-#{record.status.raw_value == 'present' ? 'success' : record.status.raw_value == 'late' ? 'warning' : 'danger'}">
                                        <field name="status"/>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Biometric Employee Search View -->
    <record id="view_biometric_employee_search" model="ir.ui.view">
        <field name="name">biometric.employee.search</field>
        <field name="model">biometric.employee</field>
        <field name="arch" type="xml">
            <search string="Search Employees">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department"/>
                <field name="device_id"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Present" name="present" domain="[('status', '=', 'present')]"/>
                <filter string="Late" name="late" domain="[('status', '=', 'late')]"/>
                <filter string="Absent" name="absent" domain="[('status', '=', 'absent')]"/>
                <group expand="0" string="Group By">
                    <filter string="Device" name="group_device" context="{'group_by': 'device_id'}"/>
                    <filter string="Department" name="group_department" context="{'group_by': 'department'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                    <filter string="Work Rule" name="group_work_rule" context="{'group_by': 'work_rule_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Biometric Employee Action -->
    <record id="action_biometric_employee" model="ir.actions.act_window">
        <field name="name">Biometric Employees</field>
        <field name="res_model">biometric.employee</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="view_biometric_employee_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No employees found!
            </p>
            <p>
                Employees are automatically synchronized from biometric devices.
                You can also manually add employees or sync from devices.
            </p>
        </field>
    </record>
</odoo>
